"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from mistralai.utils import validate_const
import pydantic
from pydantic.functional_validators import <PERSON><PERSON><PERSON>da<PERSON>
from typing import Literal, Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class PredictionTypedDict(TypedDict):
    type: Literal["content"]
    content: NotRequired[str]


class Prediction(BaseModel):
    TYPE: Annotated[
        Annotated[
            Optional[Literal["content"]], AfterValidator(validate_const("content"))
        ],
        pydantic.Field(alias="type"),
    ] = "content"

    content: Optional[str] = ""
