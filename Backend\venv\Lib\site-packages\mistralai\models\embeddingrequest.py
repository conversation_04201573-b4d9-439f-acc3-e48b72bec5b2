"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .embeddingdtype import EmbeddingDtype
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
import pydantic
from pydantic import model_serializer
from typing import List, Optional, Union
from typing_extensions import Annotated, NotRequired, TypeAliasType, TypedDict


EmbeddingRequestInputsTypedDict = TypeAliasType(
    "EmbeddingRequestInputsTypedDict", Union[str, List[str]]
)
r"""Text to embed."""


EmbeddingRequestInputs = TypeAliasType("EmbeddingRequestInputs", Union[str, List[str]])
r"""Text to embed."""


class EmbeddingRequestTypedDict(TypedDict):
    model: str
    r"""ID of the model to use."""
    inputs: EmbeddingRequestInputsTypedDict
    r"""Text to embed."""
    output_dimension: NotRequired[Nullable[int]]
    r"""The dimension of the output embeddings."""
    output_dtype: NotRequired[EmbeddingDtype]


class EmbeddingRequest(BaseModel):
    model: str
    r"""ID of the model to use."""

    inputs: Annotated[EmbeddingRequestInputs, pydantic.Field(alias="input")]
    r"""Text to embed."""

    output_dimension: OptionalNullable[int] = UNSET
    r"""The dimension of the output embeddings."""

    output_dtype: Optional[EmbeddingDtype] = None

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["output_dimension", "output_dtype"]
        nullable_fields = ["output_dimension"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
