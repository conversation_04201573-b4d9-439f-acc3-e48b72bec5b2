"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .classifierftmodelout import ClassifierFTModelOut, ClassifierFTModelOutTypedDict
from .completionftmodelout import CompletionFTModelOut, CompletionFTModelOutTypedDict
from .updateftmodelin import UpdateFTModelIn, UpdateFTModelInTypedDict
from mistralai.types import BaseModel
from mistralai.utils import (
    FieldMetadata,
    PathParamMetadata,
    RequestMetadata,
    get_discriminator,
)
from pydantic import Discriminator, Tag
from typing import Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


class JobsAPIRoutesFineTuningUpdateFineTunedModelRequestTypedDict(TypedDict):
    model_id: str
    r"""The ID of the model to update."""
    update_ft_model_in: UpdateFTModelInTypedDict


class JobsAPIRoutesFineTuningUpdateFineTunedModelRequest(BaseModel):
    model_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""The ID of the model to update."""

    update_ft_model_in: Annotated[
        UpdateFTModelIn,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]


JobsAPIRoutesFineTuningUpdateFineTunedModelResponseTypedDict = TypeAliasType(
    "JobsAPIRoutesFineTuningUpdateFineTunedModelResponseTypedDict",
    Union[CompletionFTModelOutTypedDict, ClassifierFTModelOutTypedDict],
)
r"""OK"""


JobsAPIRoutesFineTuningUpdateFineTunedModelResponse = Annotated[
    Union[
        Annotated[ClassifierFTModelOut, Tag("classifier")],
        Annotated[CompletionFTModelOut, Tag("completion")],
    ],
    Discriminator(lambda m: get_discriminator(m, "model_type", "model_type")),
]
r"""OK"""
