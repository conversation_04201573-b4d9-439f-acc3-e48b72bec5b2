"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .sharingin import SharingIn, SharingInTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class LibrariesShareCreateV1RequestTypedDict(TypedDict):
    library_id: str
    sharing_in: SharingInTypedDict


class LibrariesShareCreateV1Request(BaseModel):
    library_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    sharing_in: Annotated[
        SharingIn, FieldMetadata(request=RequestMetadata(media_type="application/json"))
    ]
