"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .contentchunk import ContentChunk, ContentChunkTypedDict
from .toolcall import Tool<PERSON>all, ToolCallTypedDict
from mistralai_azure.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)
from pydantic import model_serializer
from typing import List, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict


ContentTypedDict = TypeAliasType(
    "ContentTypedDict", Union[str, List[ContentChunkTypedDict]]
)


Content = TypeAliasType("Content", Union[str, List[ContentChunk]])


class DeltaMessageTypedDict(TypedDict):
    role: NotRequired[Nullable[str]]
    content: NotRequired[Nullable[ContentTypedDict]]
    tool_calls: NotRequired[Nullable[List[ToolCallTypedDict]]]


class DeltaMessage(BaseModel):
    role: OptionalNullable[str] = UNSET

    content: OptionalNullable[Content] = UNSET

    tool_calls: OptionalNullable[List[ToolCall]] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["role", "content", "tool_calls"]
        nullable_fields = ["role", "content", "tool_calls"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
