"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .file import File, FileTypedDict
from .filepurpose import FilePurpose
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, MultipartFormMetadata, validate_open_enum
from pydantic.functional_validators import PlainValida<PERSON>
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class FilesAPIRoutesUploadFileMultiPartBodyParamsTypedDict(TypedDict):
    file: FileTypedDict
    r"""The File object (not file name) to be uploaded.
    To upload a file and specify a custom file name you should format your request as such:
    ```bash
    file=@path/to/your/file.jsonl;filename=custom_name.jsonl
    ```
    Otherwise, you can just keep the original file name:
    ```bash
    file=@path/to/your/file.jsonl
    ```
    """
    purpose: NotRequired[FilePurpose]


class FilesAPIRoutesUploadFileMultiPartBodyParams(BaseModel):
    file: Annotated[File, FieldMetadata(multipart=MultipartFormMetadata(file=True))]
    r"""The File object (not file name) to be uploaded.
    To upload a file and specify a custom file name you should format your request as such:
    ```bash
    file=@path/to/your/file.jsonl;filename=custom_name.jsonl
    ```
    Otherwise, you can just keep the original file name:
    ```bash
    file=@path/to/your/file.jsonl
    ```
    """

    purpose: Annotated[
        Annotated[Optional[FilePurpose], PlainValidator(validate_open_enum(False))],
        FieldMetadata(multipart=True),
    ] = None
