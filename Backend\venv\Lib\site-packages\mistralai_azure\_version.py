"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

import importlib.metadata

__title__: str = "mistralai_azure"
__version__: str = "1.6.0"
__openapi_doc_version__: str = "0.0.2"
__gen_version__: str = "2.548.6"
__user_agent__: str = "speakeasy-sdk/python 1.6.0 2.548.6 0.0.2 mistralai_azure"

try:
    if __package__ is not None:
        __version__ = importlib.metadata.version(__package__)
except importlib.metadata.PackageNotFoundError:
    pass
