"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .libraryinupdate import LibraryInUpdate, LibraryInUpdateTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class LibrariesUpdateV1RequestTypedDict(TypedDict):
    library_id: str
    library_in_update: LibraryInUpdateTypedDict


class LibrariesUpdateV1Request(BaseModel):
    library_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    library_in_update: Annotated[
        LibraryInUpdate,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
