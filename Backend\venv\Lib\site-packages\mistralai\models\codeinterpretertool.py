"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


CodeInterpreterToolType = Literal["code_interpreter"]


class CodeInterpreterToolTypedDict(TypedDict):
    type: NotRequired[CodeInterpreterToolType]


class CodeInterpreterTool(BaseModel):
    type: Optional[CodeInterpreterToolType] = "code_interpreter"
