"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .sharingdelete import SharingDelete, SharingDeleteTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class LibrariesShareDeleteV1RequestTypedDict(TypedDict):
    library_id: str
    sharing_delete: SharingDeleteTypedDict


class LibrariesShareDeleteV1Request(BaseModel):
    library_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    sharing_delete: Annotated[
        SharingDelete,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
