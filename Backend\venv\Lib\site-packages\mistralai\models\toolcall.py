"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .functioncall import FunctionCall, FunctionCallTypedDict
from .tooltypes import ToolTypes
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import validate_open_enum
from pydantic import model_serializer
from pydantic.functional_validators import PlainValida<PERSON>
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class MetadataTypedDict(TypedDict):
    pass


class Metadata(BaseModel):
    pass


class ToolCallTypedDict(TypedDict):
    function: FunctionCallTypedDict
    id: NotRequired[str]
    type: NotRequired[ToolTypes]
    index: NotRequired[int]
    metadata: NotRequired[Nullable[MetadataTypedDict]]


class ToolCall(BaseModel):
    function: FunctionCall

    id: Optional[str] = "null"

    type: Annotated[Optional[ToolTypes], PlainValidator(validate_open_enum(False))] = (
        None
    )

    index: Optional[int] = 0

    metadata: OptionalNullable[Metadata] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["id", "type", "index", "metadata"]
        nullable_fields = ["metadata"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
