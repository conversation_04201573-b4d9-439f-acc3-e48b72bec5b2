"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


AgentConversationObject = Literal["conversation"]


class AgentConversationTypedDict(TypedDict):
    id: str
    created_at: datetime
    updated_at: datetime
    agent_id: str
    name: NotRequired[Nullable[str]]
    r"""Name given to the conversation."""
    description: NotRequired[Nullable[str]]
    r"""Description of the what the conversation is about."""
    object: NotRequired[AgentConversationObject]


class AgentConversation(BaseModel):
    id: str

    created_at: datetime

    updated_at: datetime

    agent_id: str

    name: OptionalNullable[str] = UNSET
    r"""Name given to the conversation."""

    description: OptionalNullable[str] = UNSET
    r"""Description of the what the conversation is about."""

    object: Optional[AgentConversationObject] = "conversation"

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["name", "description", "object"]
        nullable_fields = ["name", "description"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
