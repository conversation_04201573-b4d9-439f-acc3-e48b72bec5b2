"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .agenthandoffdoneevent import AgentHandoffDoneEvent, AgentHandoffDoneEventTypedDict
from .agenthandoffstartedevent import (
    AgentHandoffStartedEvent,
    AgentHandoffStartedEventTypedDict,
)
from .functioncallevent import FunctionCallEvent, FunctionCallEventTypedDict
from .messageoutputevent import MessageOutputEvent, MessageOutputEventTypedDict
from .responsedoneevent import ResponseDoneEvent, ResponseDoneEventTypedDict
from .responseerrorevent import ResponseErrorEvent, ResponseErrorEventTypedDict
from .responsestartedevent import ResponseStartedEvent, ResponseStartedEventTypedDict
from .ssetypes import SSETypes
from .toolexecutiondeltaevent import (
    ToolExecutionDeltaEvent,
    ToolExecutionDeltaEventTypedDict,
)
from .toolexecutiondoneevent import (
    ToolExecutionDoneEvent,
    ToolExecutionDoneEventTypedDict,
)
from .toolexecutionstartedevent import (
    ToolExecutionStartedEvent,
    ToolExecutionStartedEventTypedDict,
)
from mistralai.types import BaseModel
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag
from typing import Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


ConversationEventsDataTypedDict = TypeAliasType(
    "ConversationEventsDataTypedDict",
    Union[
        ResponseStartedEventTypedDict,
        ResponseDoneEventTypedDict,
        ResponseErrorEventTypedDict,
        ToolExecutionStartedEventTypedDict,
        ToolExecutionDeltaEventTypedDict,
        ToolExecutionDoneEventTypedDict,
        AgentHandoffStartedEventTypedDict,
        AgentHandoffDoneEventTypedDict,
        FunctionCallEventTypedDict,
        MessageOutputEventTypedDict,
    ],
)


ConversationEventsData = Annotated[
    Union[
        Annotated[AgentHandoffDoneEvent, Tag("agent.handoff.done")],
        Annotated[AgentHandoffStartedEvent, Tag("agent.handoff.started")],
        Annotated[ResponseDoneEvent, Tag("conversation.response.done")],
        Annotated[ResponseErrorEvent, Tag("conversation.response.error")],
        Annotated[ResponseStartedEvent, Tag("conversation.response.started")],
        Annotated[FunctionCallEvent, Tag("function.call.delta")],
        Annotated[MessageOutputEvent, Tag("message.output.delta")],
        Annotated[ToolExecutionDeltaEvent, Tag("tool.execution.delta")],
        Annotated[ToolExecutionDoneEvent, Tag("tool.execution.done")],
        Annotated[ToolExecutionStartedEvent, Tag("tool.execution.started")],
    ],
    Discriminator(lambda m: get_discriminator(m, "type", "type")),
]


class ConversationEventsTypedDict(TypedDict):
    event: SSETypes
    r"""Server side events sent when streaming a conversation response."""
    data: ConversationEventsDataTypedDict


class ConversationEvents(BaseModel):
    event: SSETypes
    r"""Server side events sent when streaming a conversation response."""

    data: ConversationEventsData
