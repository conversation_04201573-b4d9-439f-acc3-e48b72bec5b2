"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from mistralai.utils import validate_const
import pydantic
from pydantic.functional_validators import <PERSON>V<PERSON>da<PERSON>
from typing import Literal, Optional
from typing_extensions import Annotated, TypedDict


class FileChunkTypedDict(TypedDict):
    file_id: str
    type: Literal["file"]


class FileChunk(BaseModel):
    file_id: str

    TYPE: Annotated[
        Annotated[Optional[Literal["file"]], AfterValidator(validate_const("file"))],
        pydantic.Field(alias="type"),
    ] = "file"
