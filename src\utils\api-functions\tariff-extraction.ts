import { ExtractedTariffData } from '@/types/types';
import toast from 'react-hot-toast';

const TARIFF_EXTRACTION_API_URL = 'http://localhost:5000/api';

export interface TariffExtractionResponse {
  success: boolean;
  data?: ExtractedTariffData[];
  count?: number;
  error?: string;
}

/**
 * Extract tariff data from a PDF file using the backend extraction service
 * @param file - The PDF file to extract data from
 * @param useLLM - Whether to use LLM for extraction (default: true)
 * @returns Promise with extracted tariff data
 */
export async function extractTariffFromPDF(
  file: File, 
  useLLM: boolean = true
): Promise<ExtractedTariffData[]> {
  try {
    // Validate file
    if (!file) {
      throw new Error('No file provided');
    }

    if (!file.name.toLowerCase().endsWith('.pdf')) {
      throw new Error('Only PDF files are supported');
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('file', file);
    formData.append('use_llm', useLLM.toString());

    // Show loading toast
    const loadingToast = toast.loading('Extracting tariff data from PDF...');

    try {
      console.log('Sending request to:', `${TARIFF_EXTRACTION_API_URL}/extract-tariff`);
      console.log('FormData contents:', {
        file: file.name,
        size: file.size,
        type: file.type,
        use_llm: formData.get('use_llm')
      });

      const response = await fetch(`${TARIFF_EXTRACTION_API_URL}/extract-tariff`, {
        method: 'POST',
        body: formData,
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      let result: TariffExtractionResponse;
      try {
        result = await response.json();
        console.log('Response body:', result);
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError);
        const textResponse = await response.text();
        console.log('Raw response text:', textResponse);
        throw new Error(`Invalid JSON response: ${textResponse}`);
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (!response.ok) {
        console.error('Request failed with status:', response.status, 'Error:', result.error);
        throw new Error(result.error || `HTTP error! status: ${response.status}`);
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to extract tariff data');
      }

      if (!result.data || result.data.length === 0) {
        toast.error('No tariff data found in the PDF');
        return [];
      }

      toast.success(`Successfully extracted ${result.count} tariff entries`);
      return result.data;

    } catch (fetchError) {
      toast.dismiss(loadingToast);
      throw fetchError;
    }

  } catch (error) {
    console.error('Error extracting tariff from PDF:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    toast.error(`Failed to extract tariff data: ${errorMessage}`);
    
    throw error;
  }
}

/**
 * Check if the tariff extraction service is available
 * @returns Promise<boolean> - true if service is available
 */
export async function checkTariffExtractionService(): Promise<boolean> {
  try {
    const response = await fetch(`${TARIFF_EXTRACTION_API_URL}/health`, {
      method: 'GET',
    });

    const result = await response.json();
    return response.ok && result.success;

  } catch (error) {
    console.warn('Tariff extraction service is not available:', error);
    return false;
  }
}