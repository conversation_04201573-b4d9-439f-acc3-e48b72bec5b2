"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .ftclassifierlossfunction import FTClassifierLossFunction
from mistralai.types import BaseModel
from typing import List
from typing_extensions import TypedDict


class ClassifierTargetOutTypedDict(TypedDict):
    name: str
    labels: List[str]
    weight: float
    loss_function: FTClassifierLossFunction


class ClassifierTargetOut(BaseModel):
    name: str

    labels: List[str]

    weight: float

    loss_function: FTClassifierLossFunction
