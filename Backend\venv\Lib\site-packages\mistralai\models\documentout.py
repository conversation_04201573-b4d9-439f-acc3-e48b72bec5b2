"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing_extensions import NotRequired, TypedDict


class DocumentOutTypedDict(TypedDict):
    id: str
    library_id: str
    hash: str
    mime_type: str
    extension: str
    size: int
    name: str
    created_at: datetime
    processing_status: str
    uploaded_by_id: str
    uploaded_by_type: str
    tokens_processing_total: int
    summary: NotRequired[Nullable[str]]
    last_processed_at: NotRequired[Nullable[datetime]]
    number_of_pages: NotRequired[Nullable[int]]
    tokens_processing_main_content: NotRequired[Nullable[int]]
    tokens_processing_summary: NotRequired[Nullable[int]]


class DocumentOut(BaseModel):
    id: str

    library_id: str

    hash: str

    mime_type: str

    extension: str

    size: int

    name: str

    created_at: datetime

    processing_status: str

    uploaded_by_id: str

    uploaded_by_type: str

    tokens_processing_total: int

    summary: OptionalNullable[str] = UNSET

    last_processed_at: OptionalNullable[datetime] = UNSET

    number_of_pages: OptionalNullable[int] = UNSET

    tokens_processing_main_content: OptionalNullable[int] = UNSET

    tokens_processing_summary: OptionalNullable[int] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "summary",
            "last_processed_at",
            "number_of_pages",
            "tokens_processing_main_content",
            "tokens_processing_summary",
        ]
        nullable_fields = [
            "summary",
            "last_processed_at",
            "number_of_pages",
            "tokens_processing_main_content",
            "tokens_processing_summary",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
