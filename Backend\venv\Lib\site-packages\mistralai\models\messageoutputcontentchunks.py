"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .documenturlchunk import DocumentUR<PERSON>hunk, DocumentURLChunkTypedDict
from .imageurlchunk import ImageUR<PERSON>hunk, ImageUR<PERSON>hunkTypedDict
from .textchunk import Text<PERSON>hunk, TextChunkTypedDict
from .toolfilechunk import ToolFileChunk, ToolFileChunkTypedDict
from .toolreferencechunk import Tool<PERSON><PERSON>erenceChunk, ToolReferenceChunkTypedDict
from typing import Union
from typing_extensions import TypeAliasType


MessageOutputContentChunksTypedDict = TypeAliasType(
    "MessageOutputContentChunksTypedDict",
    Union[
        TextChunkTypedDict,
        ImageURLChunkTypedDict,
        DocumentURLChunkTypedDict,
        ToolFileChunkTypedDict,
        ToolReferenceChunkTypedDict,
    ],
)


MessageOutputContentChunks = TypeAliasType(
    "MessageOutputContentChunks",
    Union[
        TextChunk, ImageURLChunk, DocumentURLChunk, <PERSON>l<PERSON>ileChunk, ToolReferenceChunk
    ],
)
