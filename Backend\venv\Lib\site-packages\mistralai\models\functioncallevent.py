"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


FunctionCallEventType = Literal["function.call.delta"]


class FunctionCallEventTypedDict(TypedDict):
    id: str
    name: str
    tool_call_id: str
    arguments: str
    type: NotRequired[FunctionCallEventType]
    created_at: NotRequired[datetime]
    output_index: NotRequired[int]


class FunctionCallEvent(BaseModel):
    id: str

    name: str

    tool_call_id: str

    arguments: str

    type: Optional[FunctionCallEventType] = "function.call.delta"

    created_at: Optional[datetime] = None

    output_index: Optional[int] = 0
