"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .file import File, FileTypedDict
from mistralai.types import BaseModel
from mistralai.utils import (
    FieldMetadata,
    MultipartFormMetadata,
    PathParamMetadata,
    RequestMetadata,
)
from typing_extensions import Annotated, TypedDict


class LibrariesDocumentsUploadV1DocumentUploadTypedDict(TypedDict):
    file: FileTypedDict
    r"""The File object (not file name) to be uploaded.
    To upload a file and specify a custom file name you should format your request as such:
    ```bash
    file=@path/to/your/file.jsonl;filename=custom_name.jsonl
    ```
    Otherwise, you can just keep the original file name:
    ```bash
    file=@path/to/your/file.jsonl
    ```
    """


class LibrariesDocumentsUploadV1DocumentUpload(BaseModel):
    file: Annotated[File, FieldMetadata(multipart=MultipartFormMetadata(file=True))]
    r"""The File object (not file name) to be uploaded.
    To upload a file and specify a custom file name you should format your request as such:
    ```bash
    file=@path/to/your/file.jsonl;filename=custom_name.jsonl
    ```
    Otherwise, you can just keep the original file name:
    ```bash
    file=@path/to/your/file.jsonl
    ```
    """


class LibrariesDocumentsUploadV1RequestTypedDict(TypedDict):
    library_id: str
    request_body: LibrariesDocumentsUploadV1DocumentUploadTypedDict


class LibrariesDocumentsUploadV1Request(BaseModel):
    library_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    request_body: Annotated[
        LibrariesDocumentsUploadV1DocumentUpload,
        FieldMetadata(request=RequestMetadata(media_type="multipart/form-data")),
    ]
