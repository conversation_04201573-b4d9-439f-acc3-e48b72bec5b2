"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


FunctionResultEntryObject = Literal["entry"]

FunctionResultEntryType = Literal["function.result"]


class FunctionResultEntryTypedDict(TypedDict):
    tool_call_id: str
    result: str
    object: NotRequired[FunctionResultEntryObject]
    type: NotRequired[FunctionResultEntryType]
    created_at: NotRequired[datetime]
    completed_at: NotRequired[Nullable[datetime]]
    id: NotRequired[str]


class FunctionResultEntry(BaseModel):
    tool_call_id: str

    result: str

    object: Optional[FunctionResultEntryObject] = "entry"

    type: Optional[FunctionResultEntryType] = "function.result"

    created_at: Optional[datetime] = None

    completed_at: OptionalNullable[datetime] = UNSET

    id: Optional[str] = None

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["object", "type", "created_at", "completed_at", "id"]
        nullable_fields = ["completed_at"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
