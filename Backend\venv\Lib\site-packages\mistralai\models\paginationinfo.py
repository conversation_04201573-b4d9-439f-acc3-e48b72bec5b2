"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing_extensions import TypedDict


class PaginationInfoTypedDict(TypedDict):
    total_items: int
    total_pages: int
    current_page: int
    page_size: int
    has_more: bool


class PaginationInfo(BaseModel):
    total_items: int

    total_pages: int

    current_page: int

    page_size: int

    has_more: bool
