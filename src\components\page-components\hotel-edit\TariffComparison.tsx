import React, { useState, useEffect } from 'react';
import { TariffUpload, TariffPriceData, MealPlan, ExtractedTariffData } from '@/types/types';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { Check, X, Calendar, FileText, AlertCircle } from 'lucide-react';
import { updateTariffStatus } from '@/utils/api-functions/tariff-upload';
import toast from 'react-hot-toast';

interface TariffComparisonProps {
  tariff: TariffUpload;
  existingData: MealPlan[];
  extractedData?: ExtractedTariffData[];
  roomName: string;
  onApprove: () => void;
  onReject: () => void;
  onClose: () => void;
}

const TariffComparison: React.FC<TariffComparisonProps> = ({
  tariff,
  existingData,
  extractedData = [],
  roomName,
  onApprove,
  onReject,
  onClose
}) => {
  const [finalData, setFinalData] = useState<TariffPriceData[]>([]);
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>({});
  const [isProcessing, setIsProcessing] = useState(false);

  // Utility function to convert ExtractedTariffData to TariffPriceData
  const convertExtractedToTariffData = (extracted: ExtractedTariffData): TariffPriceData => {
    // Safety check
    if (!extracted) {
      console.warn('convertExtractedToTariffData: received undefined/null extracted data');
      return {
        mealPlanType: 'Unknown',
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        roomPrice: 0
      };
    }

    // Handle both LLM and fallback formats
    const mealPlan = extracted['Plan'] || extracted['Meal Plan'] || '';
    const price = extracted['Room Price'] || extracted['Price'] || 0;
    
    // Parse dates - handle different formats
    const parseDate = (dateStr: string): string => {
      if (!dateStr) return new Date().toISOString();

      // If already in ISO format, return as is
      if (dateStr.includes('T') || dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00.000Z`;
      }

      // Handle formats like "01 Oct", "30 Jun", "15 Apr", etc.
      const currentYear = new Date().getFullYear();
      const monthMap: Record<string, string> = {
        'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
        'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
        'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
      };

      // Handle "DD MMM" format (e.g., "01 Oct", "30 Jun")
      const ddMmmMatch = dateStr.match(/(\d{1,2})\s+([A-Za-z]{3})/);
      if (ddMmmMatch) {
        const day = ddMmmMatch[1].padStart(2, '0');
        const monthAbbr = ddMmmMatch[2].toUpperCase();
        const month = monthMap[monthAbbr] || '01';
        return `${currentYear}-${month}-${day}T00:00:00.000Z`;
      }

      // Handle "DD-MMM" format (e.g., "15-APR", "1-JUN")
      const ddDashMmmMatch = dateStr.match(/(\d{1,2})-([A-Z]{3})/);
      if (ddDashMmmMatch) {
        const day = ddDashMmmMatch[1].padStart(2, '0');
        const month = monthMap[ddDashMmmMatch[2]] || '01';
        return `${currentYear}-${month}-${day}T00:00:00.000Z`;
      }

      // Fallback: try to parse as regular date
      try {
        const parsedDate = new Date(dateStr);
        // If the parsed date is valid and not in the past century, use it
        if (!isNaN(parsedDate.getTime()) && parsedDate.getFullYear() > 1900) {
          return parsedDate.toISOString();
        }
      } catch {
        // Continue to fallback
      }

      return new Date().toISOString();
    };
    
    return {
      mealPlanType: mealPlan,
      startDate: parseDate(extracted['Start Date']),
      endDate: parseDate(extracted['End Date']),
      roomPrice: typeof price === 'string' ? parseFloat(price.replace(/[^\d.-]/g, '')) || 0 : price
    };
  };

  // Convert extracted data to internal format and filter by room category
  const convertedExtractedData: TariffPriceData[] = React.useMemo(() => {
    console.log('TariffComparison - Filtering data for room:', roomName);
    console.log('TariffComparison - All extracted data:', extractedData);

    // Filter extracted data to only include prices for the selected room category
    const filteredData = extractedData.filter(item => {
      const roomCategory = item['Room Category'];
      if (!roomCategory) return true; // Include if no room category specified

      // Normalize room names for comparison (remove extra spaces, convert to lowercase)
      const normalizeRoomName = (name: string) =>
        name.toLowerCase().replace(/\s+/g, ' ').trim();

      const extractedRoomName = normalizeRoomName(roomCategory);
      const selectedRoomName = normalizeRoomName(roomName);

      console.log(`Comparing: "${extractedRoomName}" vs "${selectedRoomName}"`);

      // Check if the extracted room category matches the selected room
      const matches = extractedRoomName === selectedRoomName ||
                     extractedRoomName.includes(selectedRoomName) ||
                     selectedRoomName.includes(extractedRoomName);

      console.log(`Match result: ${matches}`);
      return matches;
    });

    console.log('TariffComparison - Filtered data:', filteredData);
    const convertedData = filteredData.map(convertExtractedToTariffData).filter(item => item && item.mealPlanType);
    console.log('TariffComparison - Converted data:', convertedData);
    return convertedData;
  }, [extractedData, roomName]);

  // Convert existing data to TariffPriceData format for comparison
  const convertedExistingData: TariffPriceData[] = React.useMemo(() => 
    existingData.flatMap(mealPlan => {
      const result: TariffPriceData[] = [];

      for (let i = 0; i < mealPlan.startDate.length; i++) {
        if (mealPlan.startDate[i] && mealPlan.endDate[i]) {
          result.push({
            mealPlanType: mealPlan.mealPlan,
            startDate: mealPlan.startDate[i],
            endDate: mealPlan.endDate[i],
            roomPrice: mealPlan.roomPrice
          });
        }
      }

      return result;
    }), [existingData]
  );

  // Initialize selected items (all true by default)
  useEffect(() => {
    const initialSelected: Record<string, boolean> = {};

    convertedExtractedData.forEach((item, index) => {
      initialSelected[`${item.mealPlanType}-${item.startDate}-${item.endDate}-${index}`] = true;
    });

    setSelectedItems(initialSelected);
    setFinalData(convertedExtractedData);
  }, [convertedExtractedData]);

  // Update final data when selections change
  useEffect(() => {
    const newFinalData = convertedExtractedData.filter((item, index) =>
      selectedItems[`${item.mealPlanType}-${item.startDate}-${item.endDate}-${index}`]
    );

    setFinalData(newFinalData);
  }, [selectedItems, convertedExtractedData]);

  // Toggle selection of an item
  const toggleSelection = (item: TariffPriceData, index: number) => {
    const key = `${item.mealPlanType}-${item.startDate}-${item.endDate}-${index}`;
    setSelectedItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Find conflicts between existing and extracted data
  const findConflict = (item: TariffPriceData): TariffPriceData | null => {
    return convertedExistingData.find(existing =>
      existing.mealPlanType === item.mealPlanType &&
      (
        (new Date(existing.startDate) <= new Date(item.endDate) &&
         new Date(existing.endDate) >= new Date(item.startDate))
      )
    ) || null;
  };

  // Handle approval with selected data
  const handleApprove = async () => {
    if (finalData.length === 0) {
      toast.error('No tariff data selected for approval');
      return;
    }

    setIsProcessing(true);

    try {
      await updateTariffStatus(
        tariff.tariffId!,
        'approved',
        finalData,
        `Approved with ${finalData.length} price updates`
      );

      toast.success('Tariff approved with selected data');
      onApprove();
    } catch (error) {
      console.error('Error approving tariff:', error);
      toast.error('Failed to approve tariff');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle rejection
  const handleReject = async () => {
    setIsProcessing(true);

    try {
      await updateTariffStatus(
        tariff.tariffId!,
        'rejected',
        undefined,
        'Rejected by administrator'
      );

      toast.success('Tariff rejected');
      onReject();
    } catch (error) {
      console.error('Error rejecting tariff:', error);
      toast.error('Failed to reject tariff');
    } finally {
      setIsProcessing(false);
    }
  };

  // Group extracted data by meal plan type for easier comparison
  const groupedExtractedData: Record<string, { tariffData: TariffPriceData; originalData: ExtractedTariffData }[]> = React.useMemo(() => {
    const grouped: Record<string, { tariffData: TariffPriceData; originalData: ExtractedTariffData }[]> = {};

    // Filter the original extracted data first, then group
    const filteredOriginalData = extractedData.filter(item => {
      const roomCategory = item['Room Category'];
      if (!roomCategory) return true; // Include if no room category specified

      // Normalize room names for comparison (remove extra spaces, convert to lowercase)
      const normalizeRoomName = (name: string) =>
        name.toLowerCase().replace(/\s+/g, ' ').trim();

      const extractedRoomName = normalizeRoomName(roomCategory);
      const selectedRoomName = normalizeRoomName(roomName);

      // Check if the extracted room category matches the selected room
      return extractedRoomName === selectedRoomName ||
             extractedRoomName.includes(selectedRoomName) ||
             selectedRoomName.includes(extractedRoomName);
    });

    // Now group the filtered data
    filteredOriginalData.forEach((originalItem, index) => {
      const convertedItem = convertedExtractedData[index];
      if (convertedItem && convertedItem.mealPlanType) {
        if (!grouped[convertedItem.mealPlanType]) {
          grouped[convertedItem.mealPlanType] = [];
        }
        grouped[convertedItem.mealPlanType].push({
          tariffData: convertedItem,
          originalData: originalItem
        });
      }
    });
    return grouped;
  }, [extractedData, convertedExtractedData, roomName]);



  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-5xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">
            Tariff Comparison - {roomName}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Compare extracted tariff data with existing prices before approval
          </p>
        </div>
        <div className="flex items-center gap-2">
          <FileText className="text-blue-500" size={18} />
          <span className="text-sm text-gray-600">
            {tariff.filePath.split('-').pop()}
          </span>
        </div>
      </div>

      {/* Alert if no data extracted */}
      {extractedData.length === 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
          <div className="flex">
            <AlertCircle className="text-amber-500 mr-3" size={20} />
            <div>
              <h3 className="text-sm font-medium text-amber-800">No Data Extracted</h3>
              <p className="text-sm text-amber-700 mt-1">
                No price data could be extracted from this PDF. Please review the format and try again.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Comparison content */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-700 mb-3">Price Comparison</h3>

        {/* Meal Plan Tabs */}
        <div className="mb-4 border-b">
          {Object.keys(groupedExtractedData).length === 0 && (
            <p className="text-gray-500 italic py-2">No meal plans found in uploaded tariff</p>
          )}

          {Object.keys(groupedExtractedData).map(mealPlanType => (
            <div key={mealPlanType} className="mb-6">
              <h4 className="text-md font-semibold bg-gray-50 p-3 border-l-4 border-blue-500">
                {mealPlanType} Plan
              </h4>

              <div className="mt-3 overflow-x-auto">
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Select
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Season
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date Range
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        New Price (₹)
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Existing Price (₹)
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Change
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {groupedExtractedData[mealPlanType].map(({ tariffData: item, originalData }, index) => {
                      const conflict = findConflict(item);
                      const isSelected = selectedItems[`${item.mealPlanType}-${item.startDate}-${item.endDate}-${index}`];
                      const priceChange = conflict ? item.roomPrice - conflict.roomPrice : item.roomPrice;
                      const changePercent = conflict ? (priceChange / conflict.roomPrice) * 100 : 0;

                      return (
                        <tr
                          key={`${item.startDate}-${item.endDate}-${index}`}
                          className={`${isSelected ? 'bg-blue-50' : 'bg-white'} hover:bg-gray-50`}
                        >
                          <td className="px-4 py-3">
                            <input
                              type="checkbox"
                              checked={!!isSelected}
                              onChange={() => toggleSelection(item, index)}
                              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                          <td className="px-4 py-3 text-sm">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {originalData.Season}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {(() => {
                              try {
                                const startDate = new Date(item.startDate);
                                const endDate = new Date(item.endDate);
                                return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
                              } catch (error) {
                                return `${item.startDate} - ${item.endDate}`;
                              }
                            })()}
                          </td>
                          <td className="px-4 py-3 text-sm font-medium">
                            ₹{item.roomPrice.toFixed(2)}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {conflict ? (
                              <>₹{conflict.roomPrice.toFixed(2)}</>
                            ) : (
                              <span className="text-gray-400">No existing data</span>
                            )}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {conflict ? (
                              <span className={`${priceChange > 0 ? 'text-green-600' : priceChange < 0 ? 'text-red-600' : 'text-gray-500'}`}>
                                {priceChange > 0 ? '+' : ''}{priceChange.toFixed(2)}
                                ({changePercent > 0 ? '+' : ''}{changePercent.toFixed(1)}%)
                              </span>
                            ) : (
                              <span className="text-blue-600">New price</span>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Calendar View Button */}
      <div className="flex justify-center mb-6">
        <Button
          variant="outline"
          className="text-blue-700 border-blue-200 hover:bg-blue-50"
          onClick={() => window.open(`/hotels/edit/${tariff.hotelId}?view=calendar&room=${tariff.roomId}`, '_blank')}
        >
          <Calendar size={16} className="mr-2" />
          Open in Price Calendar
        </Button>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-4 mt-6 pt-4 border-t">
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          variant="outline"
          onClick={handleReject}
          disabled={isProcessing}
          className="text-red-600 border-red-200 hover:bg-red-50"
        >
          <X size={16} className="mr-1.5" />
          Reject Tariff
        </Button>
        <Button
          onClick={handleApprove}
          disabled={finalData.length === 0 || isProcessing}
          className="bg-green-600 text-white hover:bg-green-700"
        >
          {isProcessing ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            <>
              <Check size={16} className="mr-1.5" />
              Approve Selected ({finalData.length})
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default TariffComparison;