"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .agentconversation import AgentConversation, AgentConversationTypedDict
from .modelconversation import ModelConversation, ModelConversationTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata
from typing import Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


class AgentsAPIV1ConversationsGetRequestTypedDict(TypedDict):
    conversation_id: str
    r"""ID of the conversation from which we are fetching metadata."""


class AgentsAPIV1ConversationsGetRequest(BaseModel):
    conversation_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""ID of the conversation from which we are fetching metadata."""


AgentsAPIV1ConversationsGetResponseV1ConversationsGetTypedDict = TypeAliasType(
    "AgentsAPIV1ConversationsGetResponseV1ConversationsGetTypedDict",
    Union[AgentConversationTypedDict, ModelConversationTypedDict],
)
r"""Successful Response"""


AgentsAPIV1ConversationsGetResponseV1ConversationsGet = TypeAliasType(
    "AgentsAPIV1ConversationsGetResponseV1ConversationsGet",
    Union[AgentConversation, ModelConversation],
)
r"""Successful Response"""
