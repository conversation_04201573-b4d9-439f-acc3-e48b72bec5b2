"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing_extensions import NotRequired, TypedDict


class LibraryOutTypedDict(TypedDict):
    id: str
    name: str
    created_at: datetime
    updated_at: datetime
    owner_id: str
    owner_type: str
    total_size: int
    nb_documents: int
    chunk_size: Nullable[int]
    emoji: NotRequired[Nullable[str]]
    description: NotRequired[Nullable[str]]
    generated_name: NotRequired[Nullable[str]]
    generated_description: NotRequired[Nullable[str]]
    explicit_user_members_count: NotRequired[Nullable[int]]
    explicit_workspace_members_count: NotRequired[Nullable[int]]
    org_sharing_role: NotRequired[Nullable[str]]


class LibraryOut(BaseModel):
    id: str

    name: str

    created_at: datetime

    updated_at: datetime

    owner_id: str

    owner_type: str

    total_size: int

    nb_documents: int

    chunk_size: Nullable[int]

    emoji: OptionalNullable[str] = UNSET

    description: OptionalNullable[str] = UNSET

    generated_name: OptionalNullable[str] = UNSET

    generated_description: OptionalNullable[str] = UNSET

    explicit_user_members_count: OptionalNullable[int] = UNSET

    explicit_workspace_members_count: OptionalNullable[int] = UNSET

    org_sharing_role: OptionalNullable[str] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "emoji",
            "description",
            "generated_name",
            "generated_description",
            "explicit_user_members_count",
            "explicit_workspace_members_count",
            "org_sharing_role",
        ]
        nullable_fields = [
            "chunk_size",
            "emoji",
            "description",
            "generated_name",
            "generated_description",
            "explicit_user_members_count",
            "explicit_workspace_members_count",
            "org_sharing_role",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
