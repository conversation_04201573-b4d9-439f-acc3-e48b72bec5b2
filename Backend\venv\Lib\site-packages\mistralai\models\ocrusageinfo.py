"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing_extensions import NotRequired, TypedDict


class OCRUsageInfoTypedDict(TypedDict):
    pages_processed: int
    r"""Number of pages processed"""
    doc_size_bytes: NotRequired[Nullable[int]]
    r"""Document size in bytes"""


class OCRUsageInfo(BaseModel):
    pages_processed: int
    r"""Number of pages processed"""

    doc_size_bytes: OptionalNullable[int] = UNSET
    r"""Document size in bytes"""

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["doc_size_bytes"]
        nullable_fields = ["doc_size_bytes"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
