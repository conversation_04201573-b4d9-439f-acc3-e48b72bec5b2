"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata
from typing_extensions import Annotated, TypedDict


class AgentsAPIV1ConversationsMessagesRequestTypedDict(TypedDict):
    conversation_id: str
    r"""ID of the conversation from which we are fetching messages."""


class AgentsAPIV1ConversationsMessagesRequest(BaseModel):
    conversation_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""ID of the conversation from which we are fetching messages."""
