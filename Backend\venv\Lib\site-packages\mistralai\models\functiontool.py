"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .function import Function, FunctionTypedDict
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


FunctionToolType = Literal["function"]


class FunctionToolTypedDict(TypedDict):
    function: FunctionTypedDict
    type: NotRequired[FunctionToolType]


class FunctionTool(BaseModel):
    function: Function

    type: Optional[FunctionToolType] = "function"
