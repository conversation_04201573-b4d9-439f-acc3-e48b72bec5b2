"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
import pydantic
from pydantic import ConfigDict
from typing import Any, Dict, Literal, Optional
from typing_extensions import NotRequired, TypedDict


Type = Literal["transcription_segment"]


class TranscriptionSegmentChunkTypedDict(TypedDict):
    text: str
    start: float
    end: float
    type: NotRequired[Type]


class TranscriptionSegmentChunk(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True, arbitrary_types_allowed=True, extra="allow"
    )
    __pydantic_extra__: Dict[str, Any] = pydantic.Field(init=False)

    text: str

    start: float

    end: float

    type: Optional[Type] = "transcription_segment"

    @property
    def additional_properties(self):
        return self.__pydantic_extra__

    @additional_properties.setter
    def additional_properties(self, value):
        self.__pydantic_extra__ = value  # pyright: ignore[reportIncompatibleVariableOverride]
