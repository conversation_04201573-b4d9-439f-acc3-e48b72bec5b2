"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .ocrpageobject import OCRPageObject, OCRPageObjectTypedDict
from .ocrusageinfo import OCRUsageInfo, OCRUsageInfoTypedDict
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import List
from typing_extensions import NotRequired, TypedDict


class OCRResponseTypedDict(TypedDict):
    pages: List[OCRPageObjectTypedDict]
    r"""List of OCR info for pages."""
    model: str
    r"""The model used to generate the OCR."""
    usage_info: OCRUsageInfoTypedDict
    document_annotation: NotRequired[Nullable[str]]
    r"""Formatted response in the request_format if provided in json str"""


class OCRResponse(BaseModel):
    pages: List[OCRPageObject]
    r"""List of OCR info for pages."""

    model: str
    r"""The model used to generate the OCR."""

    usage_info: OCRUsageInfo

    document_annotation: OptionalNullable[str] = UNSET
    r"""Formatted response in the request_format if provided in json str"""

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["document_annotation"]
        nullable_fields = ["document_annotation"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
