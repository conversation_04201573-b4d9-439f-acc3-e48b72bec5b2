"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .messageoutputcontentchunks import (
    MessageOutputContentChunks,
    MessageOutputContentChunksTypedDict,
)
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import List, Literal, Optional, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict


MessageOutputEntryObject = Literal["entry"]

MessageOutputEntryType = Literal["message.output"]

MessageOutputEntryRole = Literal["assistant"]

MessageOutputEntryContentTypedDict = TypeAliasType(
    "MessageOutputEntryContentTypedDict",
    Union[str, List[MessageOutputContentChunksTypedDict]],
)


MessageOutputEntryContent = TypeAliasType(
    "MessageOutputEntryContent", Union[str, List[MessageOutputContentChunks]]
)


class MessageOutputEntryTypedDict(TypedDict):
    content: MessageOutputEntryContentTypedDict
    object: NotRequired[MessageOutputEntryObject]
    type: NotRequired[MessageOutputEntryType]
    created_at: NotRequired[datetime]
    completed_at: NotRequired[Nullable[datetime]]
    id: NotRequired[str]
    agent_id: NotRequired[Nullable[str]]
    model: NotRequired[Nullable[str]]
    role: NotRequired[MessageOutputEntryRole]


class MessageOutputEntry(BaseModel):
    content: MessageOutputEntryContent

    object: Optional[MessageOutputEntryObject] = "entry"

    type: Optional[MessageOutputEntryType] = "message.output"

    created_at: Optional[datetime] = None

    completed_at: OptionalNullable[datetime] = UNSET

    id: Optional[str] = None

    agent_id: OptionalNullable[str] = UNSET

    model: OptionalNullable[str] = UNSET

    role: Optional[MessageOutputEntryRole] = "assistant"

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "object",
            "type",
            "created_at",
            "completed_at",
            "id",
            "agent_id",
            "model",
            "role",
        ]
        nullable_fields = ["completed_at", "agent_id", "model"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
