"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .file import File, FileTypedDict
from .timestampgranularity import TimestampGranularity
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import FieldMetadata, MultipartFormMetadata, validate_const
import pydantic
from pydantic import model_serializer
from pydantic.functional_validators import AfterValidator
from typing import List, Literal, Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class AudioTranscriptionRequestTypedDict(TypedDict):
    model: str
    file: NotRequired[FileTypedDict]
    file_url: NotRequired[Nullable[str]]
    r"""Url of a file to be transcribed"""
    file_id: NotRequired[Nullable[str]]
    r"""ID of a file uploaded to /v1/files"""
    language: NotRequired[Nullable[str]]
    r"""Language of the audio, e.g. 'en'. Providing the language can boost accuracy."""
    temperature: NotRequired[Nullable[float]]
    stream: Literal[False]
    timestamp_granularities: NotRequired[List[TimestampGranularity]]
    r"""Granularities of timestamps to include in the response."""


class AudioTranscriptionRequest(BaseModel):
    model: Annotated[str, FieldMetadata(multipart=True)]

    file: Annotated[
        Optional[File], FieldMetadata(multipart=MultipartFormMetadata(file=True))
    ] = None

    file_url: Annotated[OptionalNullable[str], FieldMetadata(multipart=True)] = UNSET
    r"""Url of a file to be transcribed"""

    file_id: Annotated[OptionalNullable[str], FieldMetadata(multipart=True)] = UNSET
    r"""ID of a file uploaded to /v1/files"""

    language: Annotated[OptionalNullable[str], FieldMetadata(multipart=True)] = UNSET
    r"""Language of the audio, e.g. 'en'. Providing the language can boost accuracy."""

    temperature: Annotated[OptionalNullable[float], FieldMetadata(multipart=True)] = (
        UNSET
    )

    STREAM: Annotated[
        Annotated[Optional[Literal[False]], AfterValidator(validate_const(False))],
        pydantic.Field(alias="stream"),
        FieldMetadata(multipart=True),
    ] = False

    timestamp_granularities: Annotated[
        Optional[List[TimestampGranularity]], FieldMetadata(multipart=True)
    ] = None
    r"""Granularities of timestamps to include in the response."""

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "file",
            "file_url",
            "file_id",
            "language",
            "temperature",
            "stream",
            "timestamp_granularities",
        ]
        nullable_fields = ["file_url", "file_id", "language", "temperature"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
