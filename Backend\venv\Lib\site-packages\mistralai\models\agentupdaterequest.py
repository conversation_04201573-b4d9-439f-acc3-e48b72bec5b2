"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .codeinterpretertool import CodeInterpreterTool, CodeInterpreterToolTypedDict
from .completionargs import CompletionArgs, CompletionArgsTypedDict
from .documentlibrarytool import DocumentLibraryTool, DocumentLibraryToolTypedDict
from .functiontool import FunctionTool, FunctionToolTypedDict
from .imagegenerationtool import ImageGenerationTool, ImageGenerationToolTypedDict
from .websearchpremiumtool import WebSearchPremiumTool, WebSearchPremiumToolTypedDict
from .websearchtool import WebSearchTool, WebSearchToolTypedDict
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag, model_serializer
from typing import List, Optional, Union
from typing_extensions import Annotated, NotRequired, TypeAliasType, TypedDict


AgentUpdateRequestToolsTypedDict = TypeAliasType(
    "AgentUpdateRequestToolsTypedDict",
    Union[
        WebSearchToolTypedDict,
        WebSearchPremiumToolTypedDict,
        CodeInterpreterToolTypedDict,
        ImageGenerationToolTypedDict,
        FunctionToolTypedDict,
        DocumentLibraryToolTypedDict,
    ],
)


AgentUpdateRequestTools = Annotated[
    Union[
        Annotated[CodeInterpreterTool, Tag("code_interpreter")],
        Annotated[DocumentLibraryTool, Tag("document_library")],
        Annotated[FunctionTool, Tag("function")],
        Annotated[ImageGenerationTool, Tag("image_generation")],
        Annotated[WebSearchTool, Tag("web_search")],
        Annotated[WebSearchPremiumTool, Tag("web_search_premium")],
    ],
    Discriminator(lambda m: get_discriminator(m, "type", "type")),
]


class AgentUpdateRequestTypedDict(TypedDict):
    instructions: NotRequired[Nullable[str]]
    r"""Instruction prompt the model will follow during the conversation."""
    tools: NotRequired[List[AgentUpdateRequestToolsTypedDict]]
    r"""List of tools which are available to the model during the conversation."""
    completion_args: NotRequired[CompletionArgsTypedDict]
    r"""White-listed arguments from the completion API"""
    model: NotRequired[Nullable[str]]
    name: NotRequired[Nullable[str]]
    description: NotRequired[Nullable[str]]
    handoffs: NotRequired[Nullable[List[str]]]


class AgentUpdateRequest(BaseModel):
    instructions: OptionalNullable[str] = UNSET
    r"""Instruction prompt the model will follow during the conversation."""

    tools: Optional[List[AgentUpdateRequestTools]] = None
    r"""List of tools which are available to the model during the conversation."""

    completion_args: Optional[CompletionArgs] = None
    r"""White-listed arguments from the completion API"""

    model: OptionalNullable[str] = UNSET

    name: OptionalNullable[str] = UNSET

    description: OptionalNullable[str] = UNSET

    handoffs: OptionalNullable[List[str]] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "instructions",
            "tools",
            "completion_args",
            "model",
            "name",
            "description",
            "handoffs",
        ]
        nullable_fields = ["instructions", "model", "name", "description", "handoffs"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
