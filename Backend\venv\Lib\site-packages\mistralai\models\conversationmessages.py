"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .messageentries import MessageEntries, MessageEntriesTypedDict
from mistralai.types import BaseModel
from typing import List, Literal, Optional
from typing_extensions import NotRequired, TypedDict


ConversationMessagesObject = Literal["conversation.messages"]


class ConversationMessagesTypedDict(TypedDict):
    r"""Similar to the conversation history but only keep the messages"""

    conversation_id: str
    messages: List[MessageEntriesTypedDict]
    object: NotRequired[ConversationMessagesObject]


class ConversationMessages(BaseModel):
    r"""Similar to the conversation history but only keep the messages"""

    conversation_id: str

    messages: List[MessageEntries]

    object: Optional[ConversationMessagesObject] = "conversation.messages"
